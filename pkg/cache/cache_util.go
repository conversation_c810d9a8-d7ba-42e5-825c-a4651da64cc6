package cache

import (
	"context"
	"errors"
	"math/rand"
	"time"

	"app_service/global"
	"app_service/pkg/util"

	log "e.coding.net/g-dtay0385/common/go-logger"
	"github.com/go-redis/redis/v8"
)

const (
	// NullCacheValue 空值缓存标记
	NullCacheValue = "__NULL_CACHE__"
	// NullCacheTTL 空值缓存过期时间（较短，避免长时间缓存错误的空值）
	NullCacheTTL = 2 * time.Minute
)

// CacheOptions 缓存选项
type CacheOptions struct {
	// BaseTTL 基础过期时间
	BaseTTL time.Duration
	// RandomFactor 随机因子，范围0-1，实际TTL = BaseTTL + random(0, BaseTTL * RandomFactor)
	RandomFactor float64
	// EnableNullCache 是否启用空值缓存
	EnableNullCache bool
}

// DefaultCacheOptions 默认缓存选项
func DefaultCacheOptions(baseTTL time.Duration) *CacheOptions {
	return &CacheOptions{
		BaseTTL:         baseTTL,
		RandomFactor:    0.1, // 10%的随机时间
		EnableNullCache: true,
	}
}

// GetRandomTTL 获取随机TTL，防止缓存雪崩
func (opts *CacheOptions) GetRandomTTL() time.Duration {
	if opts.RandomFactor <= 0 {
		return opts.BaseTTL
	}

	// 计算随机时间：0 到 BaseTTL * RandomFactor
	randomDuration := time.Duration(float64(opts.BaseTTL) * opts.RandomFactor * rand.Float64())
	return opts.BaseTTL + randomDuration
}

// SetNullCache 设置空值缓存，防止缓存穿透
func SetNullCache(ctx context.Context, key string) error {
	if err := global.REDIS.Set(ctx, key, NullCacheValue, NullCacheTTL).Err(); err != nil {
		log.Ctx(ctx).Errorf("设置空值缓存失败 key:%s, err:%v", key, err)
		return err
	}
	return nil
}

// IsNullCache 检查是否为空值缓存
func IsNullCache(value string) bool {
	return value == NullCacheValue
}

// GetFromCache 通用缓存获取函数，支持空值缓存检测
func GetFromCache(ctx context.Context, cacheKey string, target interface{}) error {
	cachedData, err := global.REDIS.Get(ctx, cacheKey).Result()

	switch {
	case err == nil:
		// 检查是否为空值缓存
		if cachedData == NullCacheValue {
			return errors.New("数据不存在")
		}

		// 正常缓存，使用MessagePack反序列化
		if err := util.MsgpackUnmarshal([]byte(cachedData), target); err != nil {
			log.Ctx(ctx).Errorf("MessagePack解析缓存数据失败 key:%s, err:%v", cacheKey, err)
			return errors.New("缓存数据格式异常")
		}
		return nil

	case errors.Is(err, redis.Nil):
		// 缓存未命中
		return err

	default:
		log.Ctx(ctx).Errorf("Redis 获取数据失败 key:%s, err:%v", cacheKey, err)
		return errors.New("缓存服务异常")
	}
}

// GetFromCacheWithBool 返回bool值的缓存获取函数（兼容原GetCache接口）
func GetFromCacheWithBool(ctx context.Context, cacheKey string, target interface{}) (bool, error) {
	err := GetFromCache(ctx, cacheKey, target)

	switch {
	case err == nil:
		return true, nil // 缓存命中且有数据
	case errors.Is(err, redis.Nil):
		return false, nil // 缓存未命中
	case err.Error() == "数据不存在":
		return false, nil // 空值缓存，返回false表示数据不存在
	default:
		return false, err // 其他错误
	}
}

// SetToCache 通用缓存设置函数，支持随机TTL防雪崩
func SetToCache(ctx context.Context, cacheKey string, value interface{}, ttl time.Duration) error {
	// 使用MessagePack序列化，性能更好，存储空间更小
	cacheData, err := util.MsgpackMarshal(value)
	if err != nil {
		log.Ctx(ctx).Errorf("MessagePack序列化缓存数据失败 key:%s, err:%v", cacheKey, err)
		return errors.New("序列化缓存数据失败: " + err.Error())
	}

	if err := global.REDIS.Set(ctx, cacheKey, cacheData, ttl).Err(); err != nil {
		log.Ctx(ctx).Errorf("Redis 写入数据失败 key:%s, ttl:%v, err:%v", cacheKey, ttl, err)
		return errors.New("缓存服务异常")
	}
	return nil
}

// SetToCacheWithOptions 带选项的缓存设置函数，支持随机TTL
func SetToCacheWithOptions(ctx context.Context, cacheKey string, value interface{}, opts *CacheOptions) error {
	// 使用MessagePack序列化，性能更好，存储空间更小
	cacheData, err := util.MsgpackMarshal(value)
	if err != nil {
		log.Ctx(ctx).Errorf("MessagePack序列化缓存数据失败 key:%s, err:%v", cacheKey, err)
		return errors.New("序列化缓存数据失败: " + err.Error())
	}

	// 获取随机TTL，防止缓存雪崩
	ttl := opts.GetRandomTTL()
	if err := global.REDIS.Set(ctx, cacheKey, cacheData, ttl).Err(); err != nil {
		log.Ctx(ctx).Errorf("Redis 写入数据失败 key:%s, ttl:%v, err:%v", cacheKey, ttl, err)
		return errors.New("缓存服务异常")
	}
	return nil
}
